# -*- coding: utf-8 -*-
"""
测试构建环境
"""

import sys
import os
from pathlib import Path

def test_python_version():
    """测试Python版本"""
    print("=== Python版本检查 ===")
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version >= (3, 8):
        print("✓ Python版本符合要求 (>= 3.8)")
        return True
    else:
        print("❌ Python版本过低，需要3.8或更高版本")
        return False

def test_dependencies():
    """测试依赖包"""
    print("\n=== 依赖包检查 ===")
    
    dependencies = [
        ('PyInstaller', 'pyinstaller'),
        ('httpx', 'httpx'),
        ('playwright', 'playwright'),
        ('pandas', 'pandas'),
        ('openpyxl', 'openpyxl'),
        ('pydantic', 'pydantic'),
        ('tenacity', 'tenacity'),
    ]
    
    results = []
    for name, module in dependencies:
        try:
            __import__(module)
            print(f"✓ {name}")
            results.append(True)
        except ImportError:
            print(f"❌ {name} - 未安装")
            results.append(False)
    
    return all(results)

def test_required_files():
    """测试必需文件"""
    print("\n=== 必需文件检查 ===")
    
    required_files = [
        "run.py",
        "main_gui.py",
        "config_manager.py",
        "data_exporter.py",
        "browser_manager.py",
        "browser_install_dialog.py",
        "smart_browser_installer.py",
        "app/xhs_search.py",
        "app/stealth.min.js",
        "requirements.txt"
    ]
    
    results = []
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✓ {file_path}")
            results.append(True)
        else:
            print(f"❌ {file_path} - 文件不存在")
            results.append(False)
    
    return all(results)

def test_build_scripts():
    """测试构建脚本"""
    print("\n=== 构建脚本检查 ===")
    
    build_files = [
        "build_exe.py",
        "quick_build.py",
        "build.bat"
    ]
    
    results = []
    for file_path in build_files:
        if Path(file_path).exists():
            print(f"✓ {file_path}")
            results.append(True)
        else:
            print(f"❌ {file_path} - 文件不存在")
            results.append(False)
    
    return all(results)

def test_import_main():
    """测试主模块导入"""
    print("\n=== 主模块导入测试 ===")
    
    modules = [
        'main_gui',
        'config_manager',
        'data_exporter',
        'browser_manager',
        'browser_install_dialog',
        'smart_browser_installer'
    ]
    
    results = []
    for module in modules:
        try:
            __import__(module)
            print(f"✓ {module}")
            results.append(True)
        except ImportError as e:
            print(f"❌ {module} - 导入失败: {e}")
            results.append(False)
        except Exception as e:
            print(f"⚠ {module} - 导入警告: {e}")
            results.append(True)  # 警告不算失败
    
    return all(results)

def test_disk_space():
    """测试磁盘空间"""
    print("\n=== 磁盘空间检查 ===")
    
    try:
        import shutil
        total, used, free = shutil.disk_usage(".")
        free_gb = free / (1024**3)
        
        print(f"可用空间: {free_gb:.1f} GB")
        
        if free_gb >= 2:
            print("✓ 磁盘空间充足")
            return True
        else:
            print("❌ 磁盘空间不足，建议至少2GB可用空间")
            return False
    except Exception as e:
        print(f"⚠ 无法检查磁盘空间: {e}")
        return True

def provide_recommendations(results):
    """提供建议"""
    print("\n=== 建议和解决方案 ===")
    
    if not results['python']:
        print("🔧 Python版本问题:")
        print("   - 请安装Python 3.8或更高版本")
        print("   - 下载地址: https://www.python.org/downloads/")
    
    if not results['dependencies']:
        print("🔧 依赖包问题:")
        print("   - 运行: pip install -r requirements.txt")
        print("   - 或者: pip install pyinstaller httpx playwright pandas openpyxl")
    
    if not results['files']:
        print("🔧 文件缺失问题:")
        print("   - 确保所有源代码文件都存在")
        print("   - 检查项目完整性")
    
    if not results['imports']:
        print("🔧 导入问题:")
        print("   - 检查Python路径设置")
        print("   - 确保所有依赖都已正确安装")
    
    if all(results.values()):
        print("🎉 环境检查全部通过！")
        print("💡 构建建议:")
        print("   1. 运行 build.bat 进行一键构建")
        print("   2. 或者运行 python quick_build.py 快速构建")
        print("   3. 或者运行 python build_exe.py 完整构建")

def main():
    """主函数"""
    print("=" * 50)
    print("小红书数据采集工具 - 构建环境测试")
    print("=" * 50)
    
    # 执行所有测试
    results = {
        'python': test_python_version(),
        'dependencies': test_dependencies(),
        'files': test_required_files(),
        'scripts': test_build_scripts(),
        'imports': test_import_main(),
        'disk': test_disk_space()
    }
    
    # 统计结果
    passed = sum(results.values())
    total = len(results)
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    
    for test_name, result in results.items():
        status = "✓" if result else "❌"
        print(f"{status} {test_name}")
    
    # 提供建议
    provide_recommendations(results)
    
    # 返回总体结果
    return passed == total

if __name__ == "__main__":
    success = main()
    print(f"\n{'='*50}")
    if success:
        print("🎉 构建环境检查通过！可以开始构建exe文件。")
    else:
        print("❌ 构建环境存在问题，请根据建议进行修复。")
    print(f"{'='*50}")
    
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
