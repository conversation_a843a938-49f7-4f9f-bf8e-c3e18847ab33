# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['run.py'],
    pathex=[],
    binaries=[],
    datas=[('app', 'app'), ('utils', 'utils'), ('app/stealth.min.js', '.')],
    hiddenimports=['asyncio', 'playwright', 'playwright.sync_api', 'playwright.async_api', 'pandas', 'openpyxl', 'tkinter', 'tkinter.ttk', 'tkinter.messagebox', 'tkinter.filedialog', 'tkinter.scrolledtext', 'threading', 'subprocess', 'shutil', 'pathlib', 'json', 'csv', 'datetime', 'urllib.parse', 'urllib.request', 'ssl', 'certifi', 'httpx', 'pydantic', 'tenacity', 'browser_manager', 'browser_install_dialog', 'smart_browser_installer', 'app', 'app.xhs_search', 'app.client', 'app.login', 'utils.utils', 'utils.base_config', 'utils.crawler_util'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='小红书数据采集工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
