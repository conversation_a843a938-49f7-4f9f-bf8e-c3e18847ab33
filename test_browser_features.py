# -*- coding: utf-8 -*-
"""
测试浏览器相关新功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_config_manager_browser_features():
    """测试配置管理器的浏览器相关功能"""
    print("测试配置管理器浏览器功能...")
    
    try:
        from config_manager import config_manager
        
        # 测试自定义浏览器缓存路径功能
        test_cache_path = "C:\\test_browser_cache"
        config_manager.set_custom_browser_cache_path(test_cache_path, True)
        
        assert config_manager.get_custom_browser_cache_path() == test_cache_path
        assert config_manager.is_using_custom_browser_cache() == True
        
        # 测试自动安装浏览器配置
        config_manager.set_auto_install_browser(True)
        assert config_manager.is_auto_install_browser() == True
        
        # 测试Playwright浏览器路径配置
        test_playwright_path = "C:\\test_playwright_browsers"
        config_manager.set_playwright_browser_path(test_playwright_path)
        assert config_manager.get_playwright_browser_path() == test_playwright_path
        
        # 测试获取浏览器缓存目录（当自定义路径不存在时应该返回默认路径）
        cache_dir = config_manager.get_browser_cache_dir()
        print(f"浏览器缓存目录: {cache_dir}")
        
        # 重置为默认
        config_manager.set_custom_browser_cache_path("", False)
        config_manager.set_auto_install_browser(True)
        
        print("✓ 配置管理器浏览器功能测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 配置管理器浏览器功能测试失败: {e}")
        return False

def test_browser_manager():
    """测试浏览器管理器"""
    print("\n测试浏览器管理器...")
    
    try:
        from browser_manager import BrowserManager
        
        # 创建浏览器管理器实例
        manager = BrowserManager()
        
        # 测试基本方法
        assert hasattr(manager, 'check_browser_installed')
        assert hasattr(manager, 'install_browser')
        assert hasattr(manager, 'ensure_browser_available')
        assert hasattr(manager, 'get_browser_executable_path')
        assert hasattr(manager, 'setup_playwright_environment')
        
        print("✓ 浏览器管理器方法存在")
        
        # 测试环境设置
        manager.setup_playwright_environment()
        print("✓ Playwright环境设置成功")
        
        # 测试浏览器信息获取
        browser_info = manager.get_browser_info()
        print(f"浏览器信息: {browser_info}")
        print("✓ 浏览器信息获取成功")
        
        print("✓ 浏览器管理器测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 浏览器管理器测试失败: {e}")
        return False

def test_gui_browser_features():
    """测试GUI浏览器功能"""
    print("\n测试GUI浏览器功能...")
    
    try:
        from main_gui import XHSSpiderGUI
        
        # 创建GUI实例
        app = XHSSpiderGUI()
        
        # 检查新的浏览器相关变量
        assert hasattr(app, 'auto_install_browser_var')
        assert hasattr(app, 'use_custom_cache_var')
        assert hasattr(app, 'cache_path_var')
        assert hasattr(app, 'cache_path_entry')
        assert hasattr(app, 'cache_browse_button')
        
        print("✓ GUI浏览器功能组件存在")
        
        # 检查新的回调方法
        assert hasattr(app, 'browse_cache_path')
        assert hasattr(app, 'on_cache_path_change')
        assert hasattr(app, 'on_browser_type_change')
        
        print("✓ GUI浏览器功能回调方法存在")
        
        # 销毁窗口
        app.root.destroy()
        
        print("✓ GUI浏览器功能测试通过")
        return True
        
    except Exception as e:
        print(f"✗ GUI浏览器功能测试失败: {e}")
        return False

def test_playwright_import():
    """测试Playwright导入"""
    print("\n测试Playwright导入...")
    
    try:
        import playwright
        print(f"✓ Playwright版本: {playwright.__version__}")
        
        from playwright.async_api import async_playwright
        print("✓ Playwright async_api 导入成功")
        
        return True
        
    except ImportError as e:
        print(f"✗ Playwright导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ Playwright测试失败: {e}")
        return False

def test_browser_environment():
    """测试浏览器环境"""
    print("\n测试浏览器环境...")
    
    try:
        from browser_manager import browser_manager
        
        # 设置环境
        browser_manager.setup_playwright_environment()
        print("✓ Playwright环境设置成功")
        
        # 检查浏览器安装状态
        is_installed = browser_manager.check_browser_installed()
        print(f"浏览器安装状态: {'已安装' if is_installed else '未安装'}")
        
        # 获取浏览器信息
        info = browser_manager.get_browser_info()
        print(f"浏览器路径: {info['browsers_path']}")
        print(f"Chromium安装状态: {info['chromium_installed']}")
        print(f"自动安装启用: {info['auto_install_enabled']}")
        
        if info['chromium_path']:
            print(f"Chromium可执行文件: {info['chromium_path']}")
        
        print("✓ 浏览器环境测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 浏览器环境测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("浏览器相关新功能测试")
    print("=" * 60)
    
    tests = [
        ("配置管理器浏览器功能", test_config_manager_browser_features),
        ("浏览器管理器", test_browser_manager),
        ("GUI浏览器功能", test_gui_browser_features),
        ("Playwright导入", test_playwright_import),
        ("浏览器环境", test_browser_environment)
    ]
    
    all_passed = True
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        if not test_func():
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("✅ 所有浏览器功能测试通过！")
        print("\n新功能包括:")
        print("1. ✓ 自动下载和安装Playwright浏览器")
        print("2. ✓ 自定义浏览器缓存路径配置")
        print("3. ✓ 浏览器环境自动检查和设置")
        print("4. ✓ GUI界面浏览器配置选项")
        print("\n可以运行程序测试完整功能:")
        print("python run.py")
    else:
        print("❌ 部分浏览器功能测试失败！请检查错误信息。")
    print("=" * 60)

if __name__ == "__main__":
    main()
