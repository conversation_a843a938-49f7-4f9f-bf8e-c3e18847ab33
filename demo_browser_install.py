# -*- coding: utf-8 -*-
"""
演示浏览器自动安装功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def demo_browser_install():
    """演示浏览器自动安装"""
    print("=" * 60)
    print("浏览器自动安装演示")
    print("=" * 60)
    
    try:
        from browser_manager import BrowserManager
        from config_manager import config_manager
        
        # 创建浏览器管理器
        manager = BrowserManager()
        
        print("1. 检查当前浏览器状态...")
        info = manager.get_browser_info()
        print(f"   浏览器路径: {info['browsers_path']}")
        print(f"   Chromium安装状态: {'已安装' if info['chromium_installed'] else '未安装'}")
        print(f"   自动安装启用: {'是' if info['auto_install_enabled'] else '否'}")
        
        if info['chromium_path']:
            print(f"   Chromium可执行文件: {info['chromium_path']}")
        
        print("\n2. 设置Playwright环境...")
        manager.setup_playwright_environment()
        print("   ✓ 环境设置完成")
        
        print("\n3. 检查浏览器是否需要安装...")
        if manager.check_browser_installed():
            print("   ✓ 浏览器已安装，无需重新安装")
        else:
            print("   ⚠ 浏览器未安装")
            
            if config_manager.is_auto_install_browser():
                print("\n4. 开始自动安装浏览器...")
                print("   这可能需要几分钟时间，请耐心等待...")
                
                # 安装浏览器
                success = manager.install_browser()
                
                if success:
                    print("   ✓ 浏览器安装成功!")
                    
                    # 再次检查状态
                    print("\n5. 验证安装结果...")
                    if manager.check_browser_installed():
                        print("   ✓ 浏览器安装验证成功")
                        exe_path = manager.get_browser_executable_path()
                        if exe_path:
                            print(f"   ✓ 浏览器可执行文件: {exe_path}")
                    else:
                        print("   ✗ 浏览器安装验证失败")
                else:
                    print("   ✗ 浏览器安装失败")
            else:
                print("   自动安装功能已禁用")
        
        print("\n" + "=" * 60)
        print("演示完成!")
        
        # 显示最终状态
        final_info = manager.get_browser_info()
        print(f"最终状态: {'浏览器已就绪' if final_info['chromium_installed'] else '浏览器未安装'}")
        print("=" * 60)
        
        return final_info['chromium_installed']
        
    except Exception as e:
        print(f"演示过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def demo_config_browser_cache():
    """演示浏览器缓存配置"""
    print("\n" + "=" * 60)
    print("浏览器缓存配置演示")
    print("=" * 60)
    
    try:
        from config_manager import config_manager
        
        print("1. 当前缓存配置:")
        print(f"   默认缓存路径: {config_manager.get_browser_data_dir()}")
        print(f"   自定义缓存路径: {config_manager.get_custom_browser_cache_path()}")
        print(f"   使用自定义路径: {'是' if config_manager.is_using_custom_browser_cache() else '否'}")
        print(f"   实际使用路径: {config_manager.get_browser_cache_dir()}")
        
        print("\n2. 演示设置自定义缓存路径...")
        test_cache_path = str(Path.home() / "xhs_browser_cache")
        print(f"   设置自定义路径: {test_cache_path}")
        
        config_manager.set_custom_browser_cache_path(test_cache_path, True)
        
        print("3. 验证配置:")
        print(f"   自定义缓存路径: {config_manager.get_custom_browser_cache_path()}")
        print(f"   使用自定义路径: {'是' if config_manager.is_using_custom_browser_cache() else '否'}")
        
        # 注意：由于路径可能不存在，实际使用路径可能还是默认路径
        actual_path = config_manager.get_browser_cache_dir()
        print(f"   实际使用路径: {actual_path}")
        
        print("\n4. 恢复默认设置...")
        config_manager.set_custom_browser_cache_path("", False)
        print(f"   恢复后使用路径: {config_manager.get_browser_cache_dir()}")
        
        print("\n✓ 缓存配置演示完成")
        return True
        
    except Exception as e:
        print(f"缓存配置演示出错: {e}")
        return False

def demo_search_config():
    """演示搜索配置"""
    print("\n" + "=" * 60)
    print("搜索配置演示")
    print("=" * 60)
    
    try:
        from config_manager import config_manager
        
        print("当前搜索配置:")
        search_config = config_manager.get_search_config()
        
        for key, value in search_config.items():
            print(f"   {key}: {value}")
        
        print("\n✓ 搜索配置演示完成")
        return True
        
    except Exception as e:
        print(f"搜索配置演示出错: {e}")
        return False

def main():
    """主函数"""
    print("小红书数据采集工具 - 浏览器功能演示")
    
    # 演示浏览器安装
    browser_ready = demo_browser_install()
    
    # 演示缓存配置
    demo_config_browser_cache()
    
    # 演示搜索配置
    demo_search_config()
    
    print("\n" + "=" * 60)
    print("总结:")
    print(f"✓ 浏览器自动安装功能: 已实现")
    print(f"✓ 浏览器缓存路径配置: 已实现")
    print(f"✓ 浏览器状态: {'就绪' if browser_ready else '需要手动安装'}")
    print("\n新功能说明:")
    print("1. 程序首次运行时会自动检查Playwright浏览器")
    print("2. 如果浏览器未安装，会自动下载安装")
    print("3. 可以自定义浏览器缓存文件保存位置")
    print("4. 支持在GUI界面中配置这些选项")
    print("=" * 60)

if __name__ == "__main__":
    main()
