@echo off
chcp 65001 >nul
echo ================================================
echo 小红书数据采集工具 - 一键构建脚本
echo ================================================
echo.

REM 检查Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

echo ✓ Python环境检查通过
echo.

REM 检查依赖
echo 检查依赖包...
python -c "import PyInstaller" >nul 2>&1
if %errorlevel% neq 0 (
    echo 📦 安装PyInstaller...
    python -m pip install pyinstaller
)

echo ✓ 依赖检查完成
echo.

REM 选择构建方式
echo 请选择构建方式:
echo 1. 快速构建 (推荐)
echo 2. 完整构建
echo.
set /p choice=请输入选择 (1-2):

if "%choice%"=="1" (
    echo.
    echo 🚀 开始快速构建...
    python quick_build.py
) else if "%choice%"=="2" (
    echo.
    echo 🔧 开始完整构建...
    python build_exe.py
) else (
    echo ❌ 无效选择，使用快速构建
    python quick_build.py
)

echo.
if exist "dist\小红书数据采集工具.exe" (
    echo ✅ 构建成功！
    echo 📁 输出文件: dist\小红书数据采集工具.exe

    echo.
    echo 🎉 构建完成！
    echo 💡 提示:
    echo   - exe文件位于 dist 目录
    echo   - 可以复制到任何Windows电脑运行
    echo   - 首次运行可能需要几分钟初始化

    echo.
    set /p open=是否打开dist目录? (y/n):
    if /i "%open%"=="y" (
        explorer dist
    )
) else (
    echo ❌ 构建失败，请检查错误信息
)

echo.
pause
