# -*- coding: utf-8 -*-
"""
快速构建脚本 - 简化版本
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """主函数"""
    print("=" * 50)
    print("小红书数据采集工具 - 快速构建")
    print("=" * 50)
    
    # 检查PyInstaller
    try:
        import PyInstaller
        print(f"✓ PyInstaller版本: {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller未安装")
        print("正在安装PyInstaller...")
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"])
    
    # 清理旧文件
    print("\n清理旧文件...")
    for dir_name in ['build', 'dist']:
        if os.path.exists(dir_name):
            import shutil
            shutil.rmtree(dir_name)
            print(f"  已删除: {dir_name}")
    
    # 构建命令
    cmd = [
        "pyinstaller",
        "--onefile",
        "--windowed",
        "--name=小红书数据采集工具",
        "--add-data=app;app",
        "--add-data=utils;utils",
        "run.py"
    ]
    
    print(f"\n执行构建命令:")
    print(f"  {' '.join(cmd)}")
    
    # 执行构建
    try:
        result = subprocess.run(cmd, check=True)
        print("\n✅ 构建成功!")
        
        # 检查输出文件
        exe_path = Path("dist/小红书数据采集工具.exe")
        if exe_path.exists():
            size_mb = exe_path.stat().st_size / (1024 * 1024)
            print(f"📁 输出文件: {exe_path}")
            print(f"📏 文件大小: {size_mb:.1f} MB")
        
        print("\n🎉 构建完成!")
        print("💡 提示:")
        print("  - exe文件位于 dist 目录")
        print("  - 首次运行可能需要几分钟初始化")
        print("  - 建议在目标机器上测试运行")
        
    except subprocess.CalledProcessError as e:
        print(f"\n❌ 构建失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
