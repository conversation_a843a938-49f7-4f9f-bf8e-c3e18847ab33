# -*- coding: utf-8 -*-
"""
智能浏览器安装器 - 处理各种Python环境和打包情况
"""

import os
import sys
import subprocess
import shutil
from typing import Optional, Callable, List, Tuple


class SmartBrowserInstaller:
    """智能浏览器安装器"""
    
    def __init__(self, install_path: str, progress_callback: Optional[Callable[[str], None]] = None):
        self.install_path = install_path
        self.progress_callback = progress_callback or print
        self.cancelled = False
        
    def log(self, message: str):
        """记录日志"""
        if self.progress_callback:
            self.progress_callback(message)
    
    def cancel(self):
        """取消安装"""
        self.cancelled = True
    
    def find_python_environments(self) -> List[Tuple[str, str]]:
        """查找所有可用的Python环境"""
        environments = []
        
        # 1. 当前Python环境
        current_exe = sys.executable
        if 'python' in os.path.basename(current_exe).lower():
            environments.append(("current", current_exe))
        
        # 2. 虚拟环境
        virtual_env = os.environ.get('VIRTUAL_ENV')
        if virtual_env:
            venv_python = os.path.join(virtual_env, 'Scripts', 'python.exe')
            if os.path.exists(venv_python):
                environments.append(("virtualenv", venv_python))
        
        # 3. Conda环境
        conda_prefix = os.environ.get('CONDA_PREFIX')
        if conda_prefix:
            conda_python = os.path.join(conda_prefix, 'python.exe')
            if os.path.exists(conda_python):
                environments.append(("conda", conda_python))
        
        # 4. 本地Python（便携版或打包版）
        current_dir = os.path.dirname(sys.executable)
        possible_paths = [
            os.path.join(current_dir, 'python.exe'),
            os.path.join(current_dir, 'Scripts', 'python.exe'),
            os.path.join(current_dir, 'python', 'python.exe'),
            os.path.join(os.path.dirname(current_dir), 'python.exe'),
            os.path.join(os.path.dirname(current_dir), 'Scripts', 'python.exe'),
            os.path.join(os.path.dirname(current_dir), 'python', 'python.exe'),
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                environments.append(("local", path))
        
        # 5. 系统Python
        python_commands = ['python', 'python3', 'py']
        for cmd in python_commands:
            python_path = shutil.which(cmd)
            if python_path and python_path not in [env[1] for env in environments]:
                environments.append(("system", python_path))
        
        return environments
    
    def test_python_environment(self, python_path: str) -> Tuple[bool, bool]:
        """测试Python环境
        返回: (pip可用, playwright已安装)
        """
        try:
            # 测试pip
            pip_result = subprocess.run(
                [python_path, "-m", "pip", "--version"],
                capture_output=True,
                text=True,
                timeout=10
            )
            pip_available = pip_result.returncode == 0
            
            # 测试playwright
            playwright_result = subprocess.run(
                [python_path, "-c", "import playwright; print('OK')"],
                capture_output=True,
                text=True,
                timeout=10
            )
            playwright_installed = playwright_result.returncode == 0
            
            return pip_available, playwright_installed
            
        except Exception:
            return False, False
    
    def install_with_python(self, python_path: str) -> bool:
        """使用指定的Python安装playwright和浏览器"""
        try:
            # 设置环境变量
            env = os.environ.copy()
            env["PLAYWRIGHT_BROWSERS_PATH"] = self.install_path
            
            # 测试环境
            self.log(f"测试Python环境: {python_path}")
            pip_available, playwright_installed = self.test_python_environment(python_path)
            
            if not pip_available:
                self.log("pip不可用，跳过此环境")
                return False
            
            # 步骤1: 安装playwright包（如果需要）
            if not playwright_installed:
                self.log("安装playwright包...")
                if not self._install_playwright_package(python_path, env):
                    return False
                    
                if self.cancelled:
                    return False
            else:
                self.log("playwright包已安装")
            
            # 步骤2: 安装浏览器
            self.log("安装Chromium浏览器...")
            return self._install_browser(python_path, env)
            
        except Exception as e:
            self.log(f"使用Python {python_path} 安装时出错: {e}")
            return False
    
    def _install_playwright_package(self, python_path: str, env: dict) -> bool:
        """安装playwright包"""
        try:
            cmd = [python_path, "-m", "pip", "install", "playwright"]
            self.log(f"执行: {' '.join(cmd)}")
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                env=env,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )
            
            while True:
                if self.cancelled:
                    process.terminate()
                    return False
                
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                
                if output:
                    self.log(f"包安装: {output.strip()}")
            
            return process.returncode == 0
            
        except Exception as e:
            self.log(f"安装playwright包失败: {e}")
            return False
    
    def _install_browser(self, python_path: str, env: dict) -> bool:
        """安装浏览器"""
        try:
            cmd = [python_path, "-m", "playwright", "install", "chromium"]
            self.log(f"执行: {' '.join(cmd)}")
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                env=env,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )
            
            while True:
                if self.cancelled:
                    process.terminate()
                    return False
                
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                
                if output:
                    self.log(f"浏览器安装: {output.strip()}")
            
            return process.returncode == 0
            
        except Exception as e:
            self.log(f"安装浏览器失败: {e}")
            return False
    
    def install(self) -> bool:
        """智能安装playwright浏览器"""
        try:
            self.log("开始智能安装Playwright浏览器...")
            self.log(f"安装路径: {self.install_path}")
            
            # 确保安装目录存在
            os.makedirs(self.install_path, exist_ok=True)
            
            # 查找所有Python环境
            environments = self.find_python_environments()
            self.log(f"找到 {len(environments)} 个Python环境")
            
            for env_type, python_path in environments:
                if self.cancelled:
                    return False
                
                self.log(f"尝试使用 {env_type} Python: {python_path}")
                
                if self.install_with_python(python_path):
                    self.log("安装成功！")
                    return True
                else:
                    self.log(f"{env_type} Python安装失败，尝试下一个...")
            
            # 如果所有Python环境都失败了，尝试直接使用pip
            self.log("尝试直接使用pip安装...")
            return self._try_direct_pip_install()
            
        except Exception as e:
            self.log(f"安装过程出错: {e}")
            return False
    
    def _try_direct_pip_install(self) -> bool:
        """尝试直接使用pip安装"""
        try:
            # 尝试使用系统pip
            pip_commands = ['pip', 'pip3']
            for pip_cmd in pip_commands:
                pip_path = shutil.which(pip_cmd)
                if pip_path:
                    self.log(f"尝试使用 {pip_cmd}: {pip_path}")
                    
                    # 安装playwright
                    if self._run_pip_command([pip_path, "install", "playwright"]):
                        # 尝试安装浏览器
                        python_path = shutil.which('python') or shutil.which('python3')
                        if python_path:
                            env = os.environ.copy()
                            env["PLAYWRIGHT_BROWSERS_PATH"] = self.install_path
                            return self._install_browser(python_path, env)
            
            return False
            
        except Exception as e:
            self.log(f"直接pip安装失败: {e}")
            return False
    
    def _run_pip_command(self, cmd: List[str]) -> bool:
        """运行pip命令"""
        try:
            self.log(f"执行: {' '.join(cmd)}")
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )
            
            while True:
                if self.cancelled:
                    process.terminate()
                    return False
                
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                
                if output:
                    self.log(f"pip: {output.strip()}")
            
            return process.returncode == 0
            
        except Exception as e:
            self.log(f"pip命令执行失败: {e}")
            return False
