# 浏览器安装对话框修复

## 问题描述

之前在点击"安装浏览器"按钮时，会弹出一个和原来一样的GUI窗口，这是因为安装过程中启动了新的进程，而这个进程又创建了新的GUI实例。

## 问题根源

在 `browser_manager.py` 的 `install_browser` 方法中：

```python
cmd = [sys.executable, "-m", "playwright", "install", "chromium"]
```

当程序被打包成exe文件时，`sys.executable` 指向的是exe文件本身，执行这个命令会启动一个新的exe实例，从而创建新的GUI窗口。

## 解决方案

### 1. 创建专用安装对话框

创建了 `browser_install_dialog.py`，提供专门的浏览器安装界面：

#### 主要功能
- **模态对话框**：阻止用户操作主窗口，专注于安装过程
- **实时进度显示**：显示安装进度条和状态
- **详细日志**：实时显示安装过程的详细信息
- **取消功能**：允许用户中途取消安装
- **智能Python检测**：自动找到合适的Python可执行文件

#### 界面组件
- 安装路径显示
- 进度条（无限滚动模式）
- 状态标签
- 详细日志区域
- 取消/关闭按钮

### 2. 智能Python可执行文件检测

实现了 `_find_python_executable()` 方法，智能检测可用的Python：

```python
def _find_python_executable(self) -> str:
    """智能查找Python可执行文件"""
    current_exe = sys.executable
    
    # 如果当前是标准的Python可执行文件，直接使用
    if 'python' in os.path.basename(current_exe).lower():
        return current_exe
        
    # 如果是打包的exe，尝试查找系统Python
    python_commands = ['python', 'python3', 'py']
    for cmd in python_commands:
        python_path = shutil.which(cmd)
        if python_path:
            return python_path
    
    # 尝试在当前目录及其子目录中查找Python
    # ... 更多检测逻辑
    
    # 最后回退到pip安装方式
    return "USE_PIP"
```

#### 检测策略
1. **标准Python**：如果当前就是Python可执行文件，直接使用
2. **系统Python**：查找系统PATH中的python、python3、py命令
3. **本地Python**：在程序目录及其子目录中查找Python
4. **pip方式**：如果找到pip，使用pip安装playwright
5. **回退方案**：最后回退到当前可执行文件

### 3. 修改主GUI安装逻辑

修改了 `main_gui.py` 中的 `install_playwright_browser` 方法：

```python
def install_playwright_browser(self):
    """安装Playwright浏览器"""
    # 导入安装对话框
    from browser_install_dialog import BrowserInstallDialog
    
    # 创建并显示安装对话框
    install_dialog = BrowserInstallDialog(
        parent=self.root,
        install_path=install_path,
        on_complete=self.on_install_complete
    )
    
    # 开始安装
    install_dialog.start_install()
```

#### 主要改进
- **移除线程管理**：不再需要手动管理安装线程
- **简化回调**：只需要一个完成回调函数
- **更好的用户体验**：专用对话框提供更好的视觉反馈

### 4. 防止进程冲突

#### 环境变量设置
```python
env = os.environ.copy()
env["PLAYWRIGHT_BROWSERS_PATH"] = self.install_path
```

#### 进程创建标志
```python
creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
```

在Windows上使用 `CREATE_NO_WINDOW` 标志防止创建新的控制台窗口。

## 新功能特性

### 1. 用户体验改进

- **专用安装界面**：清晰的安装进度显示
- **实时反馈**：安装过程中的实时日志输出
- **可取消操作**：用户可以随时取消安装
- **模态对话框**：防止用户在安装过程中进行其他操作

### 2. 兼容性增强

- **打包exe支持**：完美支持PyInstaller等打包工具
- **多Python环境**：自动检测和使用合适的Python版本
- **系统集成**：优先使用系统已安装的Python
- **回退机制**：多种安装方式确保成功率

### 3. 错误处理

- **详细日志**：完整记录安装过程和错误信息
- **优雅降级**：安装失败时提供清晰的错误信息
- **资源清理**：确保进程和资源得到正确清理

## 使用流程

### 1. 用户操作
1. 点击"安装内置浏览器"按钮
2. 弹出专用安装对话框
3. 查看安装进度和日志
4. 等待安装完成或选择取消

### 2. 系统处理
1. 智能检测Python可执行文件
2. 设置安装环境变量
3. 启动安装进程（不创建新窗口）
4. 实时显示安装进度
5. 完成后更新主界面状态

## 技术细节

### 1. 线程安全
- 安装在后台线程进行
- UI更新通过 `dialog.after()` 确保线程安全
- 进程管理和资源清理

### 2. 进程管理
- 使用 `subprocess.Popen` 启动安装进程
- 实时读取输出流
- 支持进程终止和清理

### 3. 错误恢复
- 多种Python检测策略
- 安装失败时的状态恢复
- 用户取消时的资源清理

## 解决的问题

1. ✅ **重复窗口问题**：不再创建新的GUI实例
2. ✅ **打包exe兼容性**：完美支持打包后的exe文件
3. ✅ **用户体验**：专用安装界面，清晰的进度显示
4. ✅ **进程管理**：正确的进程创建和清理
5. ✅ **错误处理**：详细的错误信息和恢复机制

## 测试验证

通过 `test_install_dialog.py` 验证了：
- ✅ Python可执行文件检测
- ✅ 命令构建逻辑
- ✅ 环境设置
- ✅ 对话框创建

## 注意事项

1. **网络要求**：安装需要网络连接下载浏览器
2. **权限要求**：某些目录可能需要管理员权限
3. **磁盘空间**：确保有足够空间安装浏览器
4. **防火墙**：确保允许网络访问

现在点击"安装浏览器"按钮会显示专用的安装对话框，不会再出现重复的GUI窗口！
