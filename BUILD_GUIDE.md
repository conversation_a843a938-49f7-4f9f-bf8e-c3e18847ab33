# 小红书数据采集工具 - 构建指南

## 构建方法

### 方法一：快速构建（推荐）

1. **运行快速构建脚本**：
   ```bash
   python quick_build.py
   ```

2. **等待构建完成**：
   - 构建过程可能需要几分钟
   - 会自动安装PyInstaller（如果未安装）
   - 输出文件在 `dist` 目录

### 方法二：完整构建

1. **运行完整构建脚本**：
   ```bash
   python build_exe.py
   ```

2. **功能特点**：
   - 包含更多隐藏导入
   - 创建使用说明文档
   - 复制到release目录
   - 更完整的错误检查

### 方法三：手动构建

1. **安装PyInstaller**：
   ```bash
   pip install pyinstaller
   ```

2. **执行构建命令**：
   ```bash
   pyinstaller --onefile --windowed --name="小红书数据采集工具" --add-data="app;app" --add-data="utils;utils" run.py
   ```

## 构建要求

### 系统要求
- Python 3.8 或更高版本
- Windows 操作系统（推荐）
- 至少 2GB 可用磁盘空间

### 依赖包
确保已安装以下依赖：
```bash
pip install -r requirements.txt
```

主要依赖：
- httpx>=0.28.1
- playwright>=1.52.0
- pydantic>=2.11.5
- tenacity>=9.1.2
- pandas>=2.0.0
- openpyxl>=3.1.0
- pyinstaller>=6.0.0

## 构建选项说明

### PyInstaller 参数
- `--onefile`: 打包成单个exe文件
- `--windowed`: 不显示控制台窗口
- `--name`: 设置exe文件名
- `--add-data`: 添加数据文件
- `--hidden-import`: 添加隐藏导入

### 可选参数
- `--icon=icon.ico`: 设置程序图标
- `--upx`: 使用UPX压缩（需要安装UPX）
- `--clean`: 清理缓存
- `--noconfirm`: 不询问覆盖

## 构建输出

### 文件结构
```
dist/
├── 小红书数据采集工具.exe    # 主程序
└── (其他依赖文件)

release/                      # 完整构建输出
├── 小红书数据采集工具.exe
└── 使用说明.txt
```

### 文件大小
- 预期大小：50-100 MB
- 包含所有依赖和Python运行时
- 首次运行会创建数据目录

## 常见问题

### Q: 构建失败，提示缺少模块？
A: 检查requirements.txt中的依赖是否都已安装：
```bash
pip install -r requirements.txt
```

### Q: exe文件太大？
A: 可以尝试以下优化：
1. 使用 `--exclude-module` 排除不需要的模块
2. 使用 `--upx` 压缩（需要安装UPX）
3. 考虑使用 `--onedir` 模式

### Q: 运行exe时出错？
A: 常见解决方案：
1. 确保目标机器有足够权限
2. 检查防病毒软件是否误报
3. 在开发机器上测试exe文件
4. 查看错误日志

### Q: 打包后缺少文件？
A: 使用 `--add-data` 添加缺失的文件：
```bash
--add-data="文件路径;目标路径"
```

## 优化建议

### 减小文件大小
1. **排除不需要的模块**：
   ```bash
   --exclude-module=matplotlib
   --exclude-module=numpy
   ```

2. **使用虚拟环境**：
   ```bash
   python -m venv build_env
   build_env\Scripts\activate
   pip install -r requirements.txt
   python build_exe.py
   ```

### 提高兼容性
1. **在目标系统类似的环境中构建**
2. **测试不同Windows版本**
3. **包含必要的运行时库**

### 调试构建问题
1. **查看构建日志**：
   ```bash
   pyinstaller --log-level=DEBUG ...
   ```

2. **测试导入**：
   ```python
   python -c "import 模块名"
   ```

3. **检查隐藏导入**：
   添加 `--hidden-import=模块名`

## 发布准备

### 测试清单
- [ ] 在开发机器上运行exe
- [ ] 在干净的Windows系统上测试
- [ ] 测试所有主要功能
- [ ] 检查数据导出功能
- [ ] 验证浏览器安装功能

### 发布文件
建议包含以下文件：
- 小红书数据采集工具.exe
- 使用说明.txt
- README.md
- 浏览器功能说明.md

### 版本管理
在构建前更新版本信息：
1. 修改程序中的版本号
2. 更新使用说明
3. 记录更新日志

## 自动化构建

### 批处理脚本
创建 `build.bat`：
```batch
@echo off
echo 开始构建小红书数据采集工具...
python quick_build.py
pause
```

### GitHub Actions
可以设置自动构建流程：
```yaml
name: Build EXE
on: [push]
jobs:
  build:
    runs-on: windows-latest
    steps:
    - uses: actions/checkout@v2
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.9
    - name: Install dependencies
      run: pip install -r requirements.txt
    - name: Build EXE
      run: python build_exe.py
```

## 注意事项

1. **构建环境**：建议在干净的Python环境中构建
2. **文件路径**：确保所有文件路径使用相对路径
3. **权限问题**：某些功能可能需要管理员权限
4. **网络依赖**：程序需要网络连接才能正常工作
5. **浏览器依赖**：需要Chrome浏览器或内置浏览器

构建成功后，exe文件可以在没有Python环境的Windows系统上独立运行！
