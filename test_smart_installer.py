# -*- coding: utf-8 -*-
"""
测试智能浏览器安装器
"""

import sys
import os
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_environment_detection():
    """测试环境检测"""
    try:
        print("=== 测试环境检测 ===")
        
        from smart_browser_installer import SmartBrowserInstaller
        
        # 创建临时安装路径
        with tempfile.TemporaryDirectory() as temp_dir:
            installer = SmartBrowserInstaller(temp_dir)
            
            # 查找Python环境
            environments = installer.find_python_environments()
            
            print(f"找到 {len(environments)} 个Python环境:")
            for env_type, python_path in environments:
                print(f"  - {env_type}: {python_path}")
                
                # 测试环境
                pip_available, playwright_installed = installer.test_python_environment(python_path)
                print(f"    pip可用: {pip_available}, playwright已安装: {playwright_installed}")
        
        print("=== 环境检测测试完成 ===")
        return True
        
    except Exception as e:
        print(f"❌ 环境检测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_python_testing():
    """测试Python环境测试功能"""
    try:
        print("\n=== 测试Python环境测试 ===")
        
        from smart_browser_installer import SmartBrowserInstaller
        
        # 创建临时安装路径
        with tempfile.TemporaryDirectory() as temp_dir:
            installer = SmartBrowserInstaller(temp_dir)
            
            # 测试当前Python环境
            current_python = sys.executable
            print(f"测试当前Python: {current_python}")
            
            pip_available, playwright_installed = installer.test_python_environment(current_python)
            print(f"pip可用: {pip_available}")
            print(f"playwright已安装: {playwright_installed}")
            
            if pip_available:
                print("   ✓ pip测试通过")
            else:
                print("   ⚠ pip不可用")
                
            if playwright_installed:
                print("   ✓ playwright已安装")
            else:
                print("   ⚠ playwright未安装")
        
        print("=== Python环境测试完成 ===")
        return True
        
    except Exception as e:
        print(f"❌ Python环境测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_installation_logic():
    """测试安装逻辑（不实际安装）"""
    try:
        print("\n=== 测试安装逻辑 ===")
        
        from smart_browser_installer import SmartBrowserInstaller
        
        # 创建临时安装路径
        with tempfile.TemporaryDirectory() as temp_dir:
            print(f"临时安装路径: {temp_dir}")
            
            # 创建安装器
            messages = []
            def capture_message(msg):
                messages.append(msg)
                print(f"安装器: {msg}")
            
            installer = SmartBrowserInstaller(temp_dir, capture_message)
            
            # 查找环境
            environments = installer.find_python_environments()
            print(f"找到环境数量: {len(environments)}")
            
            # 测试第一个环境（不实际安装）
            if environments:
                env_type, python_path = environments[0]
                print(f"测试环境: {env_type} - {python_path}")
                
                pip_available, playwright_installed = installer.test_python_environment(python_path)
                print(f"环境状态: pip={pip_available}, playwright={playwright_installed}")
                
                if pip_available:
                    print("   ✓ 此环境可用于安装")
                else:
                    print("   ⚠ 此环境不可用")
            
            print(f"捕获到 {len(messages)} 条消息")
        
        print("=== 安装逻辑测试完成 ===")
        return True
        
    except Exception as e:
        print(f"❌ 安装逻辑测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_command_building():
    """测试命令构建"""
    try:
        print("\n=== 测试命令构建 ===")
        
        import shutil
        
        # 测试pip查找
        pip_commands = ['pip', 'pip3']
        for cmd in pip_commands:
            pip_path = shutil.which(cmd)
            if pip_path:
                print(f"找到 {cmd}: {pip_path}")
        
        # 测试python查找
        python_commands = ['python', 'python3', 'py']
        for cmd in python_commands:
            python_path = shutil.which(cmd)
            if python_path:
                print(f"找到 {cmd}: {python_path}")
        
        # 测试环境变量
        virtual_env = os.environ.get('VIRTUAL_ENV')
        if virtual_env:
            print(f"虚拟环境: {virtual_env}")
        
        conda_prefix = os.environ.get('CONDA_PREFIX')
        if conda_prefix:
            print(f"Conda环境: {conda_prefix}")
        
        print("=== 命令构建测试完成 ===")
        return True
        
    except Exception as e:
        print(f"❌ 命令构建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dialog_integration():
    """测试对话框集成"""
    try:
        print("\n=== 测试对话框集成 ===")
        
        # 测试导入
        from browser_install_dialog import BrowserInstallDialog
        from smart_browser_installer import SmartBrowserInstaller
        
        print("   ✓ 模块导入成功")
        
        # 测试创建（不显示GUI）
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        # 创建对话框实例
        dialog = BrowserInstallDialog(
            parent=root,
            install_path="test_path",
            on_complete=lambda success: print(f"安装完成: {success}")
        )
        
        print("   ✓ 对话框创建成功")
        
        # 清理
        dialog.dialog.destroy()
        root.destroy()
        
        print("=== 对话框集成测试完成 ===")
        return True
        
    except Exception as e:
        print(f"❌ 对话框集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试智能浏览器安装器...")
    
    results = []
    
    # 测试环境检测
    results.append(test_environment_detection())
    
    # 测试Python环境测试
    results.append(test_python_testing())
    
    # 测试安装逻辑
    results.append(test_installation_logic())
    
    # 测试命令构建
    results.append(test_command_building())
    
    # 测试对话框集成
    try:
        results.append(test_dialog_integration())
    except Exception as e:
        print(f"对话框测试跳过: {e}")
        results.append(True)  # 跳过GUI测试
    
    # 总结结果
    print(f"\n=== 测试总结 ===")
    passed = sum(results)
    total = len(results)
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("✅ 所有测试通过！")
        print("\n智能安装器功能:")
        print("1. 自动检测多种Python环境")
        print("2. 测试环境可用性")
        print("3. 智能选择最佳安装方式")
        print("4. 分步安装playwright包和浏览器")
        print("5. 多种回退策略")
        print("6. 详细的安装日志")
    else:
        print("❌ 部分测试失败，请检查相关问题")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
