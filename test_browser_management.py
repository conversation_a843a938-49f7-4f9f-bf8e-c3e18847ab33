# -*- coding: utf-8 -*-
"""
测试浏览器管理功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_browser_manager():
    """测试浏览器管理器"""
    try:
        print("=== 测试浏览器管理器 ===")
        
        from browser_manager import browser_manager
        from config_manager import config_manager
        
        print("1. 检查浏览器管理器初始化...")
        print(f"   - 浏览器路径: {browser_manager.playwright_browsers_path}")
        print(f"   - 安装状态: {browser_manager._installing}")
        print("   ✓ 浏览器管理器初始化正常")
        
        print("\n2. 检查配置管理器...")
        browser_type = config_manager.get("browser_type", "local_chrome")
        print(f"   - 浏览器类型: {browser_type}")
        print(f"   - 自动安装: {config_manager.is_auto_install_browser()}")
        
        print("\n3. 获取搜索配置...")
        search_config = config_manager.get_search_config()
        for key, value in search_config.items():
            print(f"   - {key}: {value}")
        
        print("\n4. 检查浏览器状态...")
        is_installed = browser_manager.check_browser_installed()
        print(f"   - 浏览器已安装: {is_installed}")
        
        if is_installed:
            browser_path = browser_manager.get_browser_executable_path()
            print(f"   - 浏览器路径: {browser_path}")
        
        print("\n5. 获取浏览器信息...")
        info = browser_manager.get_browser_info()
        for key, value in info.items():
            print(f"   - {key}: {value}")
        
        print("\n=== 浏览器管理器测试完成 ===")
        return True
        
    except Exception as e:
        print(f"❌ 浏览器管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_changes():
    """测试配置变更"""
    try:
        print("\n=== 测试配置变更 ===")
        
        from config_manager import config_manager
        
        print("1. 测试浏览器类型切换...")
        
        # 保存原始配置
        original_type = config_manager.get("browser_type", "local_chrome")
        print(f"   - 原始类型: {original_type}")
        
        # 切换到Playwright
        config_manager.set("browser_type", "playwright")
        config_manager.set("use_local_chrome", False)
        
        # 获取新配置
        new_config = config_manager.get_search_config()
        print(f"   - 新配置use_local_chrome: {new_config['use_local_chrome']}")
        print(f"   - 新配置browser_type: {new_config['browser_type']}")
        
        # 恢复原始配置
        config_manager.set("browser_type", original_type)
        config_manager.set("use_local_chrome", original_type == "local_chrome")
        
        print("   ✓ 配置变更测试完成")
        
        print("\n=== 配置变更测试完成 ===")
        return True
        
    except Exception as e:
        print(f"❌ 配置变更测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_creation():
    """测试GUI创建（不显示）"""
    try:
        print("\n=== 测试GUI创建 ===")
        
        # 设置环境变量以避免显示GUI
        os.environ['DISPLAY'] = ':99'  # 虚拟显示
        
        from main_gui import XHSSpiderGUI
        
        print("1. 创建GUI实例...")
        # 注意：这里只创建实例，不运行mainloop
        app = XHSSpiderGUI()
        print("   ✓ GUI实例创建成功")
        
        print("2. 检查浏览器相关变量...")
        print(f"   - browser_type_var: {app.browser_type_var.get()}")
        print(f"   - playwright_path_var: {app.playwright_path_var.get()}")
        print(f"   - browser_status_var: {app.browser_status_var.get()}")
        
        print("3. 测试浏览器检查方法...")
        # 不实际运行，只检查方法是否存在
        assert hasattr(app, 'check_playwright_browser'), "缺少check_playwright_browser方法"
        assert hasattr(app, 'install_playwright_browser'), "缺少install_playwright_browser方法"
        assert hasattr(app, 'remove_playwright_browser'), "缺少remove_playwright_browser方法"
        print("   ✓ 浏览器管理方法存在")
        
        # 销毁GUI实例
        app.root.destroy()
        
        print("\n=== GUI创建测试完成 ===")
        return True
        
    except Exception as e:
        print(f"❌ GUI创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试浏览器管理功能...")
    
    results = []
    
    # 测试浏览器管理器
    results.append(test_browser_manager())
    
    # 测试配置变更
    results.append(test_config_changes())
    
    # 测试GUI创建
    try:
        results.append(test_gui_creation())
    except Exception as e:
        print(f"GUI测试跳过: {e}")
        results.append(True)  # 跳过GUI测试
    
    # 总结结果
    print(f"\n=== 测试总结 ===")
    passed = sum(results)
    total = len(results)
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("✅ 所有测试通过！")
        print("\n新功能说明:")
        print("1. 添加了浏览器类型选择（本地Chrome vs 内置Playwright）")
        print("2. 移除了自动下载功能，改为手动管理")
        print("3. 添加了浏览器状态显示和管理按钮")
        print("4. 支持自定义Playwright浏览器安装路径")
        print("5. 在启动时检查浏览器状态，不在采集时下载")
    else:
        print("❌ 部分测试失败，请检查相关问题")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
