# -*- coding: utf-8 -*-
"""
测试GUI修复 - 验证重复界面问题是否解决
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_gui_startup():
    """测试GUI启动"""
    try:
        print("=== 测试GUI启动 ===")
        
        # 导入GUI模块
        from main_gui import XHSSpiderGUI
        
        print("1. 创建GUI实例...")
        app = XHSSpiderGUI()
        print("   ✓ GUI实例创建成功")
        
        print("2. 检查初始状态...")
        print(f"   - is_running: {app.is_running}")
        print(f"   - searcher: {app.searcher}")
        print(f"   - current_task: {app.current_task}")
        print("   ✓ 初始状态正常")
        
        print("3. 测试状态管理...")
        # 模拟开始采集的状态检查
        if app.is_running:
            print("   ⚠ 任务已在运行中")
        else:
            print("   ✓ 可以开始新任务")
        
        print("4. 测试线程检查...")
        if app.current_task and app.current_task.is_alive():
            print("   ⚠ 有活跃的任务线程")
        else:
            print("   ✓ 没有活跃的任务线程")
        
        print("\n=== GUI启动测试完成 ===")
        return True
        
    except Exception as e:
        print(f"❌ GUI启动测试失败: {e}")
        return False

def test_browser_manager():
    """测试浏览器管理器"""
    try:
        print("\n=== 测试浏览器管理器 ===")
        
        from browser_manager import browser_manager
        
        print("1. 检查浏览器管理器状态...")
        print(f"   - 安装路径: {browser_manager.playwright_browsers_path}")
        print(f"   - 正在安装: {browser_manager._installing}")
        print("   ✓ 浏览器管理器状态正常")
        
        print("2. 测试浏览器检查...")
        is_installed = browser_manager.check_browser_installed()
        print(f"   - 浏览器已安装: {is_installed}")
        
        print("3. 获取浏览器信息...")
        info = browser_manager.get_browser_info()
        for key, value in info.items():
            print(f"   - {key}: {value}")
        
        print("\n=== 浏览器管理器测试完成 ===")
        return True
        
    except Exception as e:
        print(f"❌ 浏览器管理器测试失败: {e}")
        return False

def test_config_manager():
    """测试配置管理器"""
    try:
        print("\n=== 测试配置管理器 ===")
        
        from config_manager import config_manager
        
        print("1. 检查配置...")
        search_config = config_manager.get_search_config()
        print(f"   - 搜索配置: {search_config}")
        
        print("2. 检查自动安装设置...")
        auto_install = config_manager.is_auto_install_browser()
        print(f"   - 自动安装浏览器: {auto_install}")
        
        print("\n=== 配置管理器测试完成 ===")
        return True
        
    except Exception as e:
        print(f"❌ 配置管理器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试GUI修复...")
    
    results = []
    
    # 测试配置管理器
    results.append(test_config_manager())
    
    # 测试浏览器管理器
    results.append(test_browser_manager())
    
    # 测试GUI启动
    results.append(test_gui_startup())
    
    # 总结结果
    print(f"\n=== 测试总结 ===")
    passed = sum(results)
    total = len(results)
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("✅ 所有测试通过！")
        print("\n修复说明:")
        print("1. 添加了任务状态检查，防止重复启动")
        print("2. 改进了线程管理和清理机制")
        print("3. 添加了浏览器安装锁，防止重复下载")
        print("4. 增强了错误处理和日志记录")
        print("5. 添加了进程检查，防止多实例运行")
    else:
        print("❌ 部分测试失败，请检查相关问题")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
