# -*- coding: utf-8 -*-
"""
测试浏览器安装对话框
"""

import sys
import os
import tkinter as tk
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_python_detection():
    """测试Python可执行文件检测"""
    try:
        print("=== 测试Python可执行文件检测 ===")
        
        from browser_install_dialog import BrowserInstallDialog
        
        # 创建一个临时的根窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        # 创建对话框实例（但不显示）
        dialog = BrowserInstallDialog(
            parent=root,
            install_path="test_path",
            on_complete=None
        )
        
        # 测试Python检测
        python_exe = dialog._find_python_executable()
        print(f"检测到的Python可执行文件: {python_exe}")
        
        # 检查结果
        if python_exe == "USE_PIP":
            print("   ✓ 将使用pip安装方式")
        elif os.path.exists(python_exe):
            print(f"   ✓ Python可执行文件存在: {python_exe}")
        else:
            print(f"   ⚠ Python可执行文件不存在: {python_exe}")
        
        # 清理
        dialog.dialog.destroy()
        root.destroy()
        
        print("=== Python检测测试完成 ===")
        return True
        
    except Exception as e:
        print(f"❌ Python检测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dialog_creation():
    """测试对话框创建"""
    try:
        print("\n=== 测试对话框创建 ===")
        
        from browser_install_dialog import BrowserInstallDialog
        
        # 创建根窗口
        root = tk.Tk()
        root.title("测试主窗口")
        root.geometry("400x300")
        
        def on_install_complete(success):
            print(f"安装完成回调: success={success}")
            
        def show_dialog():
            """显示安装对话框"""
            dialog = BrowserInstallDialog(
                parent=root,
                install_path="C:\\test\\browsers",
                on_complete=on_install_complete
            )
            print("   ✓ 对话框创建成功")
            
            # 不实际开始安装，只是显示界面
            # dialog.start_install()
            
        # 创建测试按钮
        test_button = tk.Button(root, text="显示安装对话框", command=show_dialog)
        test_button.pack(pady=20)
        
        info_label = tk.Label(root, text="点击按钮测试安装对话框\n（不会实际安装）")
        info_label.pack(pady=10)
        
        close_button = tk.Button(root, text="关闭测试", command=root.quit)
        close_button.pack(pady=10)
        
        print("   ✓ 测试界面创建成功")
        print("   请手动测试对话框界面...")
        
        # 运行测试界面（用户可以手动测试）
        # root.mainloop()
        
        # 自动关闭以便继续其他测试
        root.after(1000, root.quit)
        root.mainloop()
        root.destroy()
        
        print("=== 对话框创建测试完成 ===")
        return True
        
    except Exception as e:
        print(f"❌ 对话框创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_command_building():
    """测试命令构建"""
    try:
        print("\n=== 测试命令构建 ===")
        
        import shutil
        
        # 测试不同的Python环境
        current_exe = sys.executable
        print(f"当前可执行文件: {current_exe}")
        
        # 检查是否是打包的exe
        is_packaged = current_exe.endswith('.exe') and 'python' not in current_exe.lower()
        print(f"是否为打包exe: {is_packaged}")
        
        # 检查系统Python
        python_commands = ['python', 'python3', 'py']
        for cmd in python_commands:
            python_path = shutil.which(cmd)
            if python_path:
                print(f"找到系统Python ({cmd}): {python_path}")
        
        # 检查pip
        pip_commands = ['pip', 'pip3']
        for cmd in pip_commands:
            pip_path = shutil.which(cmd)
            if pip_path:
                print(f"找到pip ({cmd}): {pip_path}")
        
        print("=== 命令构建测试完成 ===")
        return True
        
    except Exception as e:
        print(f"❌ 命令构建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_environment_setup():
    """测试环境设置"""
    try:
        print("\n=== 测试环境设置 ===")
        
        # 测试环境变量设置
        test_path = "C:\\test\\browsers"
        env = os.environ.copy()
        env["PLAYWRIGHT_BROWSERS_PATH"] = test_path
        
        print(f"设置PLAYWRIGHT_BROWSERS_PATH: {test_path}")
        print(f"环境变量已设置: {'PLAYWRIGHT_BROWSERS_PATH' in env}")
        
        # 测试目录创建
        import tempfile
        with tempfile.TemporaryDirectory() as temp_dir:
            test_browsers_dir = os.path.join(temp_dir, "browsers")
            os.makedirs(test_browsers_dir, exist_ok=True)
            print(f"测试目录创建成功: {test_browsers_dir}")
            print(f"目录存在: {os.path.exists(test_browsers_dir)}")
        
        print("=== 环境设置测试完成 ===")
        return True
        
    except Exception as e:
        print(f"❌ 环境设置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试浏览器安装对话框...")
    
    results = []
    
    # 测试Python检测
    results.append(test_python_detection())
    
    # 测试命令构建
    results.append(test_command_building())
    
    # 测试环境设置
    results.append(test_environment_setup())
    
    # 测试对话框创建（可选）
    try:
        results.append(test_dialog_creation())
    except Exception as e:
        print(f"对话框测试跳过: {e}")
        results.append(True)  # 跳过GUI测试
    
    # 总结结果
    print(f"\n=== 测试总结 ===")
    passed = sum(results)
    total = len(results)
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("✅ 所有测试通过！")
        print("\n新安装对话框功能:")
        print("1. 智能检测Python可执行文件")
        print("2. 支持打包exe环境")
        print("3. 模态对话框显示安装进度")
        print("4. 实时日志显示")
        print("5. 支持取消安装")
        print("6. 防止重复窗口创建")
    else:
        print("❌ 部分测试失败，请检查相关问题")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
