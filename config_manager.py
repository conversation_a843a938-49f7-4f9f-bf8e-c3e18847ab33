# -*- coding: utf-8 -*-
"""
配置管理模块
负责管理用户配置和数据保存路径
"""

import os
import json
from typing import Dict, Any
from pathlib import Path


class ConfigManager:
    """配置管理器"""

    def __init__(self):
        # 获取项目根目录
        self.project_root = Path(__file__).parent
        # 用户数据保存在项目同级目录
        self.user_data_dir = self.project_root.parent / "xhs_spider_data"
        self.config_file = self.user_data_dir / "config.json"
        self.browser_data_dir = self.user_data_dir / "browser_data"
        self.export_dir = self.user_data_dir / "exports"

        # 确保目录存在
        self._ensure_directories()

        # 默认配置
        self.default_config = {
            "headless": False,
            "use_local_chrome": True,
            "browser_type": "local_chrome",  # 浏览器类型: local_chrome 或 playwright
            "chrome_path": self._get_default_chrome_path(),
            "search_limit": 100,
            "sort_type": "latest",
            "note_type": "all",
            "last_keywords": "",
            "last_user_ids": "",
            "export_format": "excel",
            "custom_export_path": "",  # 自定义导出路径
            "use_custom_export_path": False,  # 是否使用自定义导出路径
            "browser_cache_path": "",  # 自定义浏览器缓存路径
            "use_custom_browser_cache": False,  # 是否使用自定义浏览器缓存路径
            "auto_install_browser": False,  # 是否自动安装Playwright浏览器（默认关闭）
            "playwright_browser_path": ""  # Playwright浏览器安装路径
        }

        # 加载配置
        self.config = self._load_config()

    def _ensure_directories(self):
        """确保必要的目录存在"""
        self.user_data_dir.mkdir(exist_ok=True)
        self.browser_data_dir.mkdir(exist_ok=True)
        self.export_dir.mkdir(exist_ok=True)

    def _get_default_chrome_path(self) -> str:
        """获取默认Chrome路径"""
        common_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.getenv('USERNAME', '')),
        ]

        for path in common_paths:
            if os.path.exists(path):
                return path

        return ""

    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合并默认配置和用户配置
                merged_config = self.default_config.copy()
                merged_config.update(config)
                return merged_config
            except Exception as e:
                print(f"加载配置文件失败: {e}")
                return self.default_config.copy()
        else:
            return self.default_config.copy()

    def save_config(self):
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置文件失败: {e}")

    def get(self, key: str, default=None):
        """获取配置值"""
        return self.config.get(key, default)

    def set(self, key: str, value: Any):
        """设置配置值"""
        self.config[key] = value

    def get_browser_data_dir(self) -> str:
        """获取浏览器数据目录"""
        return str(self.browser_data_dir)

    def get_export_dir(self) -> str:
        """获取导出目录"""
        if self.config.get("use_custom_export_path", False):
            custom_path = self.config.get("custom_export_path", "")
            if custom_path and os.path.exists(custom_path):
                return custom_path
        return str(self.export_dir)

    def get_chrome_path(self) -> str:
        """获取Chrome路径"""
        return self.config.get("chrome_path", "")

    def set_chrome_path(self, path: str):
        """设置Chrome路径"""
        self.config["chrome_path"] = path
        self.save_config()

    def set_custom_export_path(self, path: str, use_custom: bool = True):
        """设置自定义导出路径"""
        self.config["custom_export_path"] = path
        self.config["use_custom_export_path"] = use_custom
        self.save_config()

    def get_custom_export_path(self) -> str:
        """获取自定义导出路径"""
        return self.config.get("custom_export_path", "")

    def is_using_custom_export_path(self) -> bool:
        """是否使用自定义导出路径"""
        return self.config.get("use_custom_export_path", False)

    def set_custom_browser_cache_path(self, path: str, use_custom: bool = True):
        """设置自定义浏览器缓存路径"""
        self.config["browser_cache_path"] = path
        self.config["use_custom_browser_cache"] = use_custom
        self.save_config()

    def get_custom_browser_cache_path(self) -> str:
        """获取自定义浏览器缓存路径"""
        return self.config.get("browser_cache_path", "")

    def is_using_custom_browser_cache(self) -> bool:
        """是否使用自定义浏览器缓存路径"""
        return self.config.get("use_custom_browser_cache", False)

    def get_browser_cache_dir(self) -> str:
        """获取浏览器缓存目录"""
        if self.config.get("use_custom_browser_cache", False):
            custom_path = self.config.get("browser_cache_path", "")
            if custom_path and os.path.exists(custom_path):
                return custom_path
        return str(self.browser_data_dir)

    def set_auto_install_browser(self, auto_install: bool):
        """设置是否自动安装浏览器"""
        self.config["auto_install_browser"] = auto_install
        self.save_config()

    def is_auto_install_browser(self) -> bool:
        """是否自动安装浏览器"""
        return self.config.get("auto_install_browser", True)

    def set_playwright_browser_path(self, path: str):
        """设置Playwright浏览器路径"""
        self.config["playwright_browser_path"] = path
        self.save_config()

    def get_playwright_browser_path(self) -> str:
        """获取Playwright浏览器路径"""
        return self.config.get("playwright_browser_path", "")

    def get_search_config(self) -> Dict[str, Any]:
        """获取搜索配置"""
        browser_type = self.config.get("browser_type", "local_chrome")
        use_local_chrome = browser_type == "local_chrome"

        # 根据浏览器类型设置Chrome路径
        chrome_path = ""
        if use_local_chrome:
            chrome_path = self.config.get("chrome_path", "")
        else:
            # 使用Playwright浏览器
            from browser_manager import browser_manager
            playwright_path = browser_manager.get_browser_executable_path()
            if playwright_path:
                chrome_path = playwright_path

        return {
            "headless": self.config.get("headless", False),
            "use_local_chrome": use_local_chrome,
            "chrome_path": chrome_path,
            "user_data_dir": self.get_browser_cache_dir(),
            "auto_install_browser": self.config.get("auto_install_browser", False),
            "playwright_browser_path": self.config.get("playwright_browser_path", ""),
            "browser_type": browser_type
        }

    def update_last_inputs(self, keywords: str = None, user_ids: str = None):
        """更新最后输入的内容"""
        if keywords is not None:
            self.config["last_keywords"] = keywords
        if user_ids is not None:
            self.config["last_user_ids"] = user_ids
        self.save_config()


# 全局配置管理器实例
config_manager = ConfigManager()
