# -*- coding: utf-8 -*-
"""
测试自动路径更新功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_config_manager_auto_update():
    """测试配置管理器自动更新功能"""
    try:
        print("=== 测试配置管理器自动更新 ===")
        
        from config_manager import config_manager
        from browser_manager import browser_manager
        
        print("1. 检查当前配置...")
        current_config = config_manager.get_search_config()
        print(f"   - 浏览器类型: {current_config['browser_type']}")
        print(f"   - 使用本地Chrome: {current_config['use_local_chrome']}")
        print(f"   - Chrome路径: {current_config['chrome_path']}")
        print(f"   - Playwright路径: {current_config['playwright_browser_path']}")
        
        print("\n2. 检查浏览器管理器状态...")
        browser_installed = browser_manager.check_browser_installed()
        print(f"   - 浏览器已安装: {browser_installed}")
        
        if browser_installed:
            browser_exe_path = browser_manager.get_browser_executable_path()
            print(f"   - 浏览器可执行文件: {browser_exe_path}")
            
            browsers_path = browser_manager.playwright_browsers_path
            print(f"   - 浏览器安装目录: {browsers_path}")
        
        print("\n3. 测试自动更新功能...")
        updated = config_manager.auto_detect_and_update_browser_paths()
        print(f"   - 路径是否更新: {updated}")
        
        if updated:
            print("\n4. 检查更新后的配置...")
            new_config = config_manager.get_search_config()
            print(f"   - 新的Chrome路径: {new_config['chrome_path']}")
            print(f"   - 新的Playwright路径: {new_config['playwright_browser_path']}")
        
        print("\n=== 配置管理器自动更新测试完成 ===")
        return True
        
    except Exception as e:
        print(f"❌ 配置管理器自动更新测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_search_config_update():
    """测试搜索配置更新"""
    try:
        print("\n=== 测试搜索配置更新 ===")
        
        from config_manager import config_manager
        
        print("1. 测试本地Chrome配置...")
        config_manager.set("browser_type", "local_chrome")
        local_config = config_manager.get_search_config()
        print(f"   - 浏览器类型: {local_config['browser_type']}")
        print(f"   - 使用本地Chrome: {local_config['use_local_chrome']}")
        print(f"   - Chrome路径: {local_config['chrome_path']}")
        
        print("\n2. 测试Playwright配置...")
        config_manager.set("browser_type", "playwright")
        playwright_config = config_manager.get_search_config()
        print(f"   - 浏览器类型: {playwright_config['browser_type']}")
        print(f"   - 使用本地Chrome: {playwright_config['use_local_chrome']}")
        print(f"   - Chrome路径: {playwright_config['chrome_path']}")
        print(f"   - Playwright路径: {playwright_config['playwright_browser_path']}")
        
        print("\n=== 搜索配置更新测试完成 ===")
        return True
        
    except Exception as e:
        print(f"❌ 搜索配置更新测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_path_detection():
    """测试路径检测功能"""
    try:
        print("\n=== 测试路径检测功能 ===")
        
        from config_manager import config_manager
        
        print("1. 测试Playwright路径检测...")
        result = config_manager.update_playwright_browser_path_from_manager()
        print(f"   - 检测结果: {result}")
        
        if result:
            playwright_path = config_manager.get("playwright_browser_path", "")
            print(f"   - 检测到的路径: {playwright_path}")
            
            if playwright_path and os.path.exists(playwright_path):
                print(f"   - 路径有效: ✓")
            else:
                print(f"   - 路径无效: ❌")
        
        print("\n=== 路径检测功能测试完成 ===")
        return True
        
    except Exception as e:
        print(f"❌ 路径检测功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_integration():
    """测试GUI集成（模拟）"""
    try:
        print("\n=== 测试GUI集成 ===")
        
        # 模拟GUI方法
        class MockGUI:
            def __init__(self):
                self.messages = []
                
            def log_message(self, message):
                self.messages.append(message)
                print(f"   GUI日志: {message}")
                
            def update_browser_path_display(self):
                from config_manager import config_manager
                playwright_path = config_manager.get("playwright_browser_path", "")
                if playwright_path:
                    browser_dir = os.path.dirname(playwright_path)
                    self.log_message(f"界面已更新浏览器路径: {browser_dir}")
                    return True
                return False
        
        print("1. 创建模拟GUI...")
        mock_gui = MockGUI()
        
        print("\n2. 模拟安装完成回调...")
        from config_manager import config_manager
        
        # 模拟安装成功
        updated = config_manager.auto_detect_and_update_browser_paths()
        if updated:
            mock_gui.log_message("浏览器路径已自动更新到配置中")
            mock_gui.update_browser_path_display()
        
        print(f"\n3. GUI消息数量: {len(mock_gui.messages)}")
        
        print("\n=== GUI集成测试完成 ===")
        return True
        
    except Exception as e:
        print(f"❌ GUI集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试自动路径更新功能...")
    
    results = []
    
    # 测试配置管理器自动更新
    results.append(test_config_manager_auto_update())
    
    # 测试搜索配置更新
    results.append(test_search_config_update())
    
    # 测试路径检测
    results.append(test_path_detection())
    
    # 测试GUI集成
    results.append(test_gui_integration())
    
    # 总结结果
    print(f"\n=== 测试总结 ===")
    passed = sum(results)
    total = len(results)
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("✅ 所有测试通过！")
        print("\n自动路径更新功能:")
        print("1. ✓ 安装完成后自动检测浏览器路径")
        print("2. ✓ 自动更新配置文件")
        print("3. ✓ 更新GUI界面显示")
        print("4. ✓ 智能路径选择和验证")
        print("5. ✓ 启动时自动检测更新")
    else:
        print("❌ 部分测试失败，请检查相关问题")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
