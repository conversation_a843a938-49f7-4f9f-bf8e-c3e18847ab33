# -*- coding: utf-8 -*-
"""
浏览器管理器 - 负责Playwright浏览器的安装和管理
"""

import os
import sys
import subprocess
import asyncio
from pathlib import Path
from typing import Optional, Callable
from config_manager import config_manager


class BrowserManager:
    """浏览器管理器"""

    def __init__(self, progress_callback: Optional[Callable[[str], None]] = None):
        self.progress_callback = progress_callback or self._default_progress_callback
        self.playwright_browsers_path = self._get_playwright_browsers_path()

    def _default_progress_callback(self, message: str):
        """默认进度回调"""
        print(message)

    def _get_playwright_browsers_path(self) -> str:
        """获取Playwright浏览器安装路径"""
        # 检查是否有自定义路径
        custom_path = config_manager.get_playwright_browser_path()
        if custom_path and os.path.exists(custom_path):
            return custom_path

        # 使用默认路径
        if os.name == 'nt':  # Windows
            # 优先使用用户数据目录
            user_data_dir = config_manager.get_browser_cache_dir()
            browsers_path = os.path.join(user_data_dir, "playwright_browsers")
            return browsers_path
        else:
            # Linux/Mac 使用默认路径
            return os.path.expanduser("~/.cache/ms-playwright")

    def check_browser_installed(self, browser_name: str = "chromium") -> bool:
        """检查浏览器是否已安装"""
        try:
            # 检查Playwright浏览器目录
            browsers_path = self.playwright_browsers_path
            if not os.path.exists(browsers_path):
                return False

            # 检查chromium目录（包括chromium-和chromium_headless_shell-）
            chromium_dirs = [d for d in os.listdir(browsers_path) if d.startswith("chromium-") or d.startswith("chromium_headless_shell-")]
            if not chromium_dirs:
                return False

            # 检查可执行文件
            for chromium_dir in chromium_dirs:
                chromium_path = os.path.join(browsers_path, chromium_dir)
                if os.name == 'nt':  # Windows
                    # 检查chrome-win子目录中的chrome.exe
                    chrome_win_path = os.path.join(chromium_path, "chrome-win", "chrome.exe")
                    if os.path.exists(chrome_win_path):
                        self.progress_callback(f"找到已安装的浏览器: {chrome_win_path}")
                        return True
                    # 也检查直接在目录中的可执行文件
                    possible_exes = ["chrome.exe", "chromium.exe"]
                    for exe_name in possible_exes:
                        exe_path = os.path.join(chromium_path, exe_name)
                        if os.path.exists(exe_path):
                            self.progress_callback(f"找到已安装的浏览器: {exe_path}")
                            return True
                else:
                    exe_path = os.path.join(chromium_path, "chrome")
                    if os.path.exists(exe_path):
                        self.progress_callback(f"找到已安装的浏览器: {exe_path}")
                        return True

            return False

        except Exception as e:
            self.progress_callback(f"检查浏览器安装状态时出错: {e}")
            return False

    def install_browser(self, browser_name: str = "chromium") -> bool:
        """安装Playwright浏览器"""
        try:
            self.progress_callback("开始安装Playwright浏览器...")
            self.progress_callback("这可能需要几分钟时间，请耐心等待...")

            # 设置环境变量指定安装路径
            env = os.environ.copy()
            env["PLAYWRIGHT_BROWSERS_PATH"] = self.playwright_browsers_path

            # 构建安装命令
            cmd = [sys.executable, "-m", "playwright", "install", browser_name]

            self.progress_callback(f"执行命令: {' '.join(cmd)}")
            self.progress_callback(f"安装路径: {self.playwright_browsers_path}")

            # 执行安装命令
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                encoding='utf-8',
                errors='ignore',
                env=env
            )

            # 实时显示安装进度
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    self.progress_callback(f"安装进度: {output.strip()}")

            return_code = process.poll()

            if return_code == 0:
                self.progress_callback("浏览器安装成功!")
                # 保存安装路径到配置
                config_manager.set_playwright_browser_path(self.playwright_browsers_path)
                return True
            else:
                self.progress_callback(f"浏览器安装失败，返回码: {return_code}")
                return False

        except Exception as e:
            self.progress_callback(f"安装浏览器时出错: {e}")
            return False

    def ensure_browser_available(self, browser_name: str = "chromium") -> bool:
        """确保浏览器可用，如果没有则自动安装"""
        try:
            # 检查是否启用自动安装
            if not config_manager.is_auto_install_browser():
                self.progress_callback("自动安装浏览器功能已禁用")
                return self.check_browser_installed(browser_name)

            # 检查浏览器是否已安装
            if self.check_browser_installed(browser_name):
                self.progress_callback("浏览器已安装，无需重新安装")
                return True

            # 浏览器未安装，尝试安装
            self.progress_callback("未检测到Playwright浏览器，开始自动安装...")
            return self.install_browser(browser_name)

        except Exception as e:
            self.progress_callback(f"确保浏览器可用时出错: {e}")
            return False

    def get_browser_executable_path(self, browser_name: str = "chromium") -> Optional[str]:
        """获取浏览器可执行文件路径"""
        try:
            browsers_path = self.playwright_browsers_path
            if not os.path.exists(browsers_path):
                return None

            # 查找chromium目录
            chromium_dirs = [d for d in os.listdir(browsers_path) if d.startswith("chromium-")]
            if not chromium_dirs:
                return None

            # 使用最新版本的chromium
            chromium_dirs.sort(reverse=True)
            chromium_dir = chromium_dirs[0]
            chromium_path = os.path.join(browsers_path, chromium_dir)

            if os.name == 'nt':  # Windows
                # 首先检查chrome-win子目录
                chrome_win_path = os.path.join(chromium_path, "chrome-win", "chrome.exe")
                if os.path.exists(chrome_win_path):
                    return chrome_win_path
                # 然后检查直接路径
                exe_path = os.path.join(chromium_path, "chrome.exe")
                if os.path.exists(exe_path):
                    return exe_path
            else:
                exe_path = os.path.join(chromium_path, "chrome")
                if os.path.exists(exe_path):
                    return exe_path

            return None

        except Exception as e:
            self.progress_callback(f"获取浏览器路径时出错: {e}")
            return None

    def setup_playwright_environment(self):
        """设置Playwright环境变量"""
        try:
            # 设置浏览器路径环境变量
            os.environ["PLAYWRIGHT_BROWSERS_PATH"] = self.playwright_browsers_path

            # 确保目录存在
            os.makedirs(self.playwright_browsers_path, exist_ok=True)

            self.progress_callback(f"Playwright环境已设置，浏览器路径: {self.playwright_browsers_path}")

        except Exception as e:
            self.progress_callback(f"设置Playwright环境时出错: {e}")

    def get_browser_info(self) -> dict:
        """获取浏览器信息"""
        info = {
            "browsers_path": self.playwright_browsers_path,
            "chromium_installed": self.check_browser_installed("chromium"),
            "chromium_path": self.get_browser_executable_path("chromium"),
            "auto_install_enabled": config_manager.is_auto_install_browser()
        }
        return info


# 全局浏览器管理器实例
browser_manager = BrowserManager()
