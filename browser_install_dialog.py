# -*- coding: utf-8 -*-
"""
浏览器安装进度对话框
"""

import tkinter as tk
from tkinter import ttk
import threading
import sys
import os
from typing import Callable, Optional


class BrowserInstallDialog:
    """浏览器安装进度对话框"""

    def __init__(self, parent, install_path: str, on_complete: Optional[Callable[[bool], None]] = None):
        self.parent = parent
        self.install_path = install_path
        self.on_complete = on_complete
        self.process = None
        self.cancelled = False

        # 创建对话框
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("安装内置浏览器")
        self.dialog.geometry("600x400")
        self.dialog.resizable(False, False)

        # 设置为模态对话框
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 居中显示
        self.center_window()

        # 创建界面
        self.create_widgets()

        # 绑定关闭事件
        self.dialog.protocol("WM_DELETE_WINDOW", self.on_close)

    def _find_python_executable(self) -> str:
        """智能查找Python可执行文件"""
        current_exe = sys.executable

        # 优先策略：尝试使用当前程序运行环境的Python
        # 这样可以确保使用相同的Python环境和已安装的包

        # 1. 如果当前是标准的Python可执行文件，直接使用
        if 'python' in os.path.basename(current_exe).lower():
            self.log_message(f"使用当前Python环境: {current_exe}")
            return current_exe

        # 2. 如果是打包的exe，尝试找到原始的Python环境
        # 检查是否有虚拟环境或conda环境
        import shutil

        # 检查环境变量中的Python路径
        possible_env_pythons = []

        # 检查VIRTUAL_ENV
        virtual_env = os.environ.get('VIRTUAL_ENV')
        if virtual_env:
            venv_python = os.path.join(virtual_env, 'Scripts', 'python.exe')
            if os.path.exists(venv_python):
                possible_env_pythons.append(venv_python)

        # 检查CONDA_PREFIX
        conda_prefix = os.environ.get('CONDA_PREFIX')
        if conda_prefix:
            conda_python = os.path.join(conda_prefix, 'python.exe')
            if os.path.exists(conda_python):
                possible_env_pythons.append(conda_python)

        # 优先使用环境Python
        for python_path in possible_env_pythons:
            self.log_message(f"找到环境Python: {python_path}")
            return python_path

        # 3. 尝试在当前目录及其子目录中查找Python（可能是便携版）
        current_dir = os.path.dirname(current_exe)
        possible_paths = [
            os.path.join(current_dir, 'python.exe'),
            os.path.join(current_dir, 'Scripts', 'python.exe'),
            os.path.join(current_dir, 'python', 'python.exe'),
            os.path.join(os.path.dirname(current_dir), 'python.exe'),
            os.path.join(os.path.dirname(current_dir), 'Scripts', 'python.exe'),
            os.path.join(os.path.dirname(current_dir), 'python', 'python.exe'),
        ]

        for path in possible_paths:
            if os.path.exists(path):
                self.log_message(f"找到本地Python: {path}")
                return path

        # 4. 最后才尝试系统Python（可能没有所需的包）
        python_commands = ['python', 'python3', 'py']
        for cmd in python_commands:
            python_path = shutil.which(cmd)
            if python_path:
                self.log_message(f"找到系统Python: {python_path}")
                return python_path

        # 5. 如果都找不到，尝试使用pip
        pip_path = shutil.which('pip') or shutil.which('pip3')
        if pip_path:
            self.log_message(f"使用pip安装: {pip_path}")
            return "USE_PIP"

        # 6. 最后回退到当前可执行文件
        self.log_message(f"回退到当前可执行文件: {current_exe}")
        return current_exe

    def center_window(self):
        """居中显示窗口"""
        self.dialog.update_idletasks()

        # 获取窗口尺寸
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()

        # 获取屏幕尺寸
        screen_width = self.dialog.winfo_screenwidth()
        screen_height = self.dialog.winfo_screenheight()

        # 计算居中位置
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2

        self.dialog.geometry(f"{width}x{height}+{x}+{y}")

    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(main_frame, text="正在安装内置浏览器", font=("Arial", 14, "bold"))
        title_label.pack(pady=(0, 10))

        # 说明文本
        info_label = ttk.Label(main_frame, text="正在下载和安装Playwright Chromium浏览器，这可能需要几分钟时间...")
        info_label.pack(pady=(0, 20))

        # 安装路径显示
        path_frame = ttk.Frame(main_frame)
        path_frame.pack(fill=tk.X, pady=(0, 20))

        ttk.Label(path_frame, text="安装路径:").pack(anchor=tk.W)
        path_text = tk.Text(path_frame, height=2, wrap=tk.WORD, state=tk.DISABLED)
        path_text.pack(fill=tk.X, pady=(5, 0))

        # 显示安装路径
        path_text.config(state=tk.NORMAL)
        path_text.insert(tk.END, self.install_path)
        path_text.config(state=tk.DISABLED)

        # 进度条
        progress_frame = ttk.Frame(main_frame)
        progress_frame.pack(fill=tk.X, pady=(0, 20))

        ttk.Label(progress_frame, text="安装进度:").pack(anchor=tk.W)
        self.progress_bar = ttk.Progressbar(progress_frame, mode='indeterminate')
        self.progress_bar.pack(fill=tk.X, pady=(5, 0))

        # 状态标签
        self.status_label = ttk.Label(progress_frame, text="准备开始安装...")
        self.status_label.pack(anchor=tk.W, pady=(5, 0))

        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="安装日志", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        # 创建日志文本框和滚动条
        log_text_frame = ttk.Frame(log_frame)
        log_text_frame.pack(fill=tk.BOTH, expand=True)

        self.log_text = tk.Text(log_text_frame, height=10, wrap=tk.WORD, state=tk.DISABLED)
        scrollbar = ttk.Scrollbar(log_text_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)

        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        self.cancel_button = ttk.Button(button_frame, text="取消", command=self.cancel_install)
        self.cancel_button.pack(side=tk.RIGHT, padx=(10, 0))

        self.close_button = ttk.Button(button_frame, text="关闭", command=self.close_dialog, state=tk.DISABLED)
        self.close_button.pack(side=tk.RIGHT)

    def log_message(self, message: str):
        """添加日志消息"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)
        self.dialog.update_idletasks()

    def update_status(self, status: str):
        """更新状态"""
        self.status_label.config(text=status)
        self.dialog.update_idletasks()

    def start_install(self):
        """开始安装"""
        self.progress_bar.start()
        self.update_status("正在安装...")
        self.log_message("开始安装Playwright浏览器...")

        # 在新线程中执行安装
        install_thread = threading.Thread(target=self._install_thread, daemon=True)
        install_thread.start()

    def _install_thread(self):
        """安装线程"""
        try:
            # 使用智能安装器
            from smart_browser_installer import SmartBrowserInstaller

            installer = SmartBrowserInstaller(
                install_path=self.install_path,
                progress_callback=lambda msg: self.dialog.after(0, lambda m=msg: self.log_message(m))
            )

            # 开始安装
            success = installer.install()

            # 检查结果
            if self.cancelled:
                installer.cancel()
                self.dialog.after(0, lambda: self._on_install_cancelled())
            elif success:
                self.dialog.after(0, lambda: self._on_install_success())
            else:
                self.dialog.after(0, lambda: self._on_install_error("所有安装方式都失败了"))

        except Exception as e:
            error_msg = f"安装过程中出错: {e}"
            self.dialog.after(0, lambda: self._on_install_error(error_msg))



    def _on_install_success(self):
        """安装成功"""
        self.progress_bar.stop()
        self.update_status("安装成功！")
        self.log_message("浏览器安装成功！")

        self.cancel_button.config(state=tk.DISABLED)
        self.close_button.config(state=tk.NORMAL)

        if self.on_complete:
            self.on_complete(True)

    def _on_install_error(self, error_msg: str):
        """安装失败"""
        self.progress_bar.stop()
        self.update_status("安装失败")
        self.log_message(error_msg)

        self.cancel_button.config(state=tk.DISABLED)
        self.close_button.config(state=tk.NORMAL)

        if self.on_complete:
            self.on_complete(False)

    def _on_install_cancelled(self):
        """安装取消"""
        self.progress_bar.stop()
        self.update_status("安装已取消")
        self.log_message("安装已被用户取消")

        self.cancel_button.config(state=tk.DISABLED)
        self.close_button.config(state=tk.NORMAL)

        if self.on_complete:
            self.on_complete(False)

    def cancel_install(self):
        """取消安装"""
        self.cancelled = True
        if self.process and self.process.poll() is None:
            try:
                self.process.terminate()
                self.log_message("正在取消安装...")
            except:
                pass

    def close_dialog(self):
        """关闭对话框"""
        self.dialog.destroy()

    def on_close(self):
        """窗口关闭事件"""
        if self.process and self.process.poll() is None:
            # 如果安装正在进行，先取消
            self.cancel_install()
        else:
            self.close_dialog()
